name: Comment-Triggered PR Review

on:
  issue_comment:
    types: [created]

jobs:
  review:
    # Only run on PRs and only for your comments
    if: github.event.issue.pull_request && github.event.comment.user.login == github.repository_owner
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
      issues: write
    steps:
      - name: Generate PR Review
        uses: augmentcode/review-pr@v0
        with:
          augment_session_auth: ${{ secrets.AUGMENT_SESSION_AUTH }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          pull_number: ${{ github.event.issue.number }}
          repo_name: ${{ github.repository }}
      
      - name: React to comment
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.reactions.createForIssueComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              comment_id: ${{ github.event.comment.id }},
              content: 'eyes'
            });
