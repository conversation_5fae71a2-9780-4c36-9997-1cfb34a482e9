import { z } from "zod";

import { createTRPCRouter, publicProcedure } from "~/server/api/trpc";
import * as cheerio from "cheerio";


// Mocked DB
interface Post {
  id: number;
  name: string;
}
const posts: Post[] = [
  {
    id: 1,
    name: "Hello World",
  },
];

async function parseTrends(html: string) {
  const $ = cheerio.load(html);

  const rows = $("#trends table.trends tbody tr, #moreTrends tbody tr");

  type item = {
    rank: number
    name: string
    tweetCount: string
    url: string
  }

  const trends: item[] = [];
  rows.each((_, tr) => {
    const $tr = $(tr);
    const rank = parseInt($tr.find('th.pos').text(), 10);
    const link = $tr.find('td.main a');
    const name = link.text().trim();
    const url = link.attr('href')!;
    const tweetCount = $tr.find('td.main .desc .small').text().trim();

    trends.push({ rank, name, tweetCount, url });
  });

  return trends;
}

export const postRouter = createTRPCRouter({
  trending: publicProcedure
    .input(z.undefined())
    .query(async () => {
      const usres = fetch("https://getdaytrends.com/united-states/")
      const deres = fetch("https://getdaytrends.com/germany/")
      const us = await (await usres).text()
      const de = await (await deres).text()

      const l = {
        us: await parseTrends(us),
        de: await parseTrends(de)
      }

      return l
    }),

  hello: publicProcedure
    .input(z.object({ text: z.string() }))
    .query(({ input }) => {
      return {
        greeting: `Hello ${input.text}`,
      };
    }),

  create: publicProcedure
    .input(z.object({ name: z.string().min(1) }))
    .mutation(async ({ input }) => {
      const post: Post = {
        id: posts.length + 1,
        name: input.name,
      };
      posts.push(post);
      return post;
    }),

  getLatest: publicProcedure.query(() => {
    return posts.at(-1) ?? null;
  }),
});
