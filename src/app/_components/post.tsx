"use client";

import { api } from "~/trpc/react";

export function LatestPost() {
  const { data, isLoading, dataUpdatedAt } = api.post.trending.useQuery(undefined, {
    refetchInterval: 1000 * 60 * 30
  })
  if (isLoading || !data) return <h1>loading...</h1>

  return (
    <div>
      <div className="text-center">
        last update {new Date(dataUpdatedAt).toLocaleString(undefined, {
          weekday: 'short',
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        })}
      </div>
      <div className="flex justify-between">
        <div className="mr-5">
          <h1 className="text-5xl font-extrabold tracking-tigh mb-2">
            🇺🇸
          </h1>

          <ul>
            {data.us.map((item, idx) =>
            (<li key={"us" + item.url}>
              <a className="underline" target="_blank"
                href={`https://x.com/search?q=${encodeURI(item.name).replaceAll("#", "%23")
                  }&src=trend_click&vertical=trends`}>
                {idx + 1}. {item.name}
              </a>
              - {item.tweetCount.substring(0, item.tweetCount.indexOf("tweets")).replaceAll("Under", "<")}
            </li>))}
          </ul>
        </div>

        <div>
          <h1 className="text-5xl font-extrabold tracking-tight mb-2">
            🇩🇪
          </h1>

          <ul>
            {data.de.map((item, idx) =>
            (<li key={"de" + item.url}>
              <a className="underline" target="_blank"
                href={`https://x.com/search?q=${encodeURI(item.name).replaceAll("#", "%23")
                  }&src=trend_click&vertical=trends`}>
                {idx + 1}. {item.name}
              </a>
              - {item.tweetCount.substring(0, item.tweetCount.indexOf("tweets")).replaceAll("Under", "<")}
            </li>))}
          </ul>
        </div>
      </div>

    </div>
  );
}
