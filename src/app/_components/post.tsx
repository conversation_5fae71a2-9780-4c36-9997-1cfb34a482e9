"use client";

import { api } from "~/trpc/react";

// Loading skeleton component
function TrendSkeleton() {
  return (
    <div className="animate-pulse space-y-4">
      {Array.from({ length: 10 }, (_, i) => (
        <div key={i} className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-6 w-6 rounded-full bg-gray-200 dark:bg-gray-700"></div>
            <div className="h-4 w-32 rounded bg-gray-200 sm:w-48 dark:bg-gray-700"></div>
          </div>
          <div className="h-3 w-16 rounded bg-gray-200 dark:bg-gray-700"></div>
        </div>
      ))}
    </div>
  );
}

// Types
interface TrendItem {
  rank: number;
  name: string;
  tweetCount: string;
  url: string;
}

// Individual trend item component
function TrendItemComponent({
  item,
  index,
}: {
  item: TrendItem;
  index: number;
}) {
  const formatTweetCount = (count: string) => {
    if (!count || count === "Under") return "<1K";
    return (
      count
        .substring(0, count.indexOf("tweets"))
        .replaceAll("Under", "<")
        .trim() || "N/A"
    );
  };

  const getRankColor = (rank: number) => {
    if (rank <= 3)
      return "bg-gradient-to-r from-yellow-400 to-orange-500 text-white";
    if (rank <= 5)
      return "bg-gradient-to-r from-blue-500 to-purple-500 text-white";
    return "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300";
  };

  return (
    <li className="group flex items-center justify-between rounded-lg p-4 transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-800/50">
      <div className="flex items-center gap-4">
        <span
          className={`flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full text-sm font-bold shadow-sm ${getRankColor(index + 1)}`}
        >
          {index + 1}
        </span>
        <a
          href={`https://x.com/search?q=${encodeURI(item.name).replaceAll("#", "%23")}&src=trend_click&vertical=trends`}
          target="_blank"
          rel="noopener noreferrer"
          className="font-medium text-gray-900 transition-colors duration-200 hover:text-blue-600 dark:text-gray-100 dark:hover:text-blue-400"
        >
          {item.name}
        </a>
      </div>
      <span className="text-sm text-gray-500 dark:text-gray-400">
        {formatTweetCount(item.tweetCount)}
      </span>
    </li>
  );
}

// Country card component
function CountryCard({
  country,
  flag,
  trends,
}: {
  country: string;
  flag: string;
  trends: TrendItem[];
}) {
  return (
    <div className="overflow-hidden rounded-2xl bg-white shadow-lg ring-1 ring-gray-200 transition-all duration-300 hover:shadow-xl dark:bg-gray-800 dark:ring-gray-700">
      <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 dark:from-gray-700 dark:to-gray-600">
        <div className="flex items-center gap-3">
          <span className="text-3xl">{flag}</span>
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">
            {country}
          </h2>
        </div>
      </div>
      <div className="p-6">
        <ul className="space-y-2">
          {trends.map((item, idx) => (
            <TrendItemComponent
              key={`${country}-${item.url}`}
              item={item}
              index={idx}
            />
          ))}
        </ul>
      </div>
    </div>
  );
}

export function LatestPost() {
  const { data, isLoading, dataUpdatedAt } = api.post.trending.useQuery(
    undefined,
    {
      refetchInterval: 1000 * 60 * 30,
    },
  );

  if (isLoading || !data) {
    return (
      <div className="space-y-8">
        {/* Loading header */}
        <div className="text-center">
          <div className="mx-auto h-6 w-48 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
        </div>

        {/* Loading cards */}
        <div className="grid gap-8 lg:grid-cols-2">
          <div className="overflow-hidden rounded-2xl bg-white p-6 shadow-lg dark:bg-gray-800">
            <div className="mb-6 flex items-center gap-3">
              <div className="h-8 w-8 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
              <div className="h-6 w-32 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
            </div>
            <TrendSkeleton />
          </div>
          <div className="overflow-hidden rounded-2xl bg-white p-6 shadow-lg dark:bg-gray-800">
            <div className="mb-6 flex items-center gap-3">
              <div className="h-8 w-8 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
              <div className="h-6 w-32 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
            </div>
            <TrendSkeleton />
          </div>
        </div>
      </div>
    );
  }

  const formatLastUpdate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString(undefined, {
      weekday: "short",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });
  };

  return (
    <div className="space-y-8">
      {/* Last Update Info */}
      <div className="text-center">
        <div className="inline-flex items-center gap-2 rounded-full bg-green-100 px-4 py-2 text-sm font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400">
          <div className="h-2 w-2 animate-pulse rounded-full bg-green-500"></div>
          Last updated: {formatLastUpdate(dataUpdatedAt)}
        </div>
      </div>

      {/* Country Cards Grid */}
      <div className="grid gap-8 lg:grid-cols-2">
        <CountryCard country="United States" flag="🇺🇸" trends={data.us} />
        <CountryCard country="Germany" flag="🇩🇪" trends={data.de} />
      </div>

      {/* Footer Info */}
      <div className="text-center">
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Data refreshes automatically every 30 minutes • Click any trend to
          search on X
        </p>
      </div>
    </div>
  );
}
