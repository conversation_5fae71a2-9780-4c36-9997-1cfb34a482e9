import { HydrateClient } from "~/trpc/server";
import { LatestPost } from "./_components/post";

export default async function Home() {
  return (
    <HydrateClient>
      <main className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        {/* Header */}
        <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-16 text-white">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="relative mx-auto max-w-4xl text-center">
            <h1 className="mb-4 text-5xl font-bold tracking-tight sm:text-6xl">
              Twitter Trends
            </h1>
            <p className="mx-auto max-w-2xl text-xl text-blue-100">
              Real-time trending topics from around the world. Stay connected
              with what's happening now.
            </p>
          </div>
        </div>

        {/* Main Content */}
        <div className="mx-auto max-w-7xl px-6 py-12">
          <LatestPost />
        </div>
      </main>
    </HydrateClient>
  );
}
